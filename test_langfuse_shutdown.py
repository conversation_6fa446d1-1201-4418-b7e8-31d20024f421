#!/usr/bin/env python3
"""
Test script to isolate the Langfuse shutdown issue
"""

import sys
import os
import time
import threading
import atexit

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

def test_langfuse_only():
    """Test just Langfuse initialization and shutdown"""
    print("🧪 Testing Langfuse initialization and shutdown...")
    
    try:
        from app.utils.langfuse_util import get_langfuse_util
        
        print("📝 Getting Langfuse util...")
        langfuse_util = get_langfuse_util()
        
        print(f"✅ Langfuse enabled: {langfuse_util.is_enabled}")
        
        if langfuse_util.is_enabled:
            print("🔄 Creating a test generation...")
            generation = langfuse_util.create_generation(
                name="test_generation",
                model="test-model",
                system_prompt="Test system prompt",
                user_prompt="Test user prompt"
            )
            
            if generation:
                print("✅ Generation created successfully")
                langfuse_util.end_generation(generation, "Test output")
                print("✅ Generation ended successfully")
            
            print("🔄 Flushing Langfuse...")
            langfuse_util.flush()
            print("✅ Flush completed")
            
            print("🔄 Shutting down Langfuse...")
            langfuse_util.shutdown()
            print("✅ Shutdown completed")
        
        print("🎉 Langfuse test completed successfully!")
        
    except Exception as e:
        print(f"❌ Langfuse test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def show_active_threads():
    """Show all active threads"""
    print("\n🧵 Active threads:")
    for thread in threading.enumerate():
        print(f"  - {thread.name}: {thread.is_alive()} (daemon: {thread.daemon})")

def cleanup_and_exit():
    """Final cleanup"""
    print("\n🔚 Final cleanup...")
    show_active_threads()
    
    # Try to shutdown Langfuse again
    try:
        from app.utils.langfuse_util import get_langfuse_util
        langfuse_util = get_langfuse_util()
        if langfuse_util.is_enabled:
            langfuse_util.shutdown()
            print("🔒 Final Langfuse shutdown completed")
    except Exception as e:
        print(f"⚠️  Final cleanup failed: {str(e)}")

if __name__ == "__main__":
    # Register cleanup
    atexit.register(cleanup_and_exit)
    
    print("🚀 Starting Langfuse shutdown test...")
    show_active_threads()
    
    test_langfuse_only()
    
    print("\n⏰ Waiting 2 seconds...")
    time.sleep(2)
    
    show_active_threads()
    
    print("\n🔚 Test completed, forcing exit...")
    cleanup_and_exit()
    
    print("💀 Calling sys.exit(0)...")
    sys.exit(0)
